apply plugin: 'com.android.application'

android {
    compileSdk 33
    buildToolsVersion '33.0.1'

    defaultConfig {
        applicationId "com.arcsoft.arcfacedemo"
        namespace "com.arcsoft.arcfacedemo"
        minSdkVersion 19
        targetSdkVersion 33
        versionCode 1
        versionName "1.0"
        vectorDrawables.useSupportLibrary = true
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a'
        }
    }

    dataBinding {
        enabled = true
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }


}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    // androidx.  preference,database,widget,etc.
    implementation 'androidx.appcompat:appcompat:1.1.0'
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    implementation "androidx.room:room-runtime:2.2.5"
    annotationProcessor "androidx.room:room-compiler:2.2.5"
    implementation 'androidx.recyclerview:recyclerview:1.1.0'
    implementation 'androidx.preference:preference:1.1.1'

    // material design
    implementation 'com.google.android.material:material:1.1.0'
    // image load lib
    implementation 'com.github.bumptech.glide:glide:4.9.0'
    // rxjava
    implementation 'io.reactivex.rxjava2:rxjava:2.2.9'
    implementation 'io.reactivex.rxjava2:rxandroid:2.1.0'
    // crash dump
    implementation 'com.iqiyi.xcrash:xcrash-android-lib:3.1.0'

    // for test
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.2.0'

}
